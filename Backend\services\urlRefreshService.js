const cron = require('node-cron');
const Content = require('../models/Content');
const { processContentUrls, needsUrlRefresh } = require('../utils/urlManager');
const { isUsingS3Storage } = require('../utils/storageHelper');
const { S3_URL_REFRESH_THRESHOLD } = require('../config/timeouts');

/**
 * Background service to proactively refresh expiring S3 URLs
 * Runs periodically to check for URLs that need refreshing
 */
class UrlRefreshService {
  constructor() {
    this.isRunning = false;
    this.cronJob = null;
    this.batchSize = 50; // Process 50 content items at a time
    this.refreshStats = {
      totalProcessed: 0,
      urlsRefreshed: 0,
      errors: 0,
      lastRun: null
    };
  }

  /**
   * Start the URL refresh service
   * Runs every 6 hours to check for expiring URLs
   */
  start() {
    if (!isUsingS3Storage()) {
      console.log('[URLRefreshService] S3 storage not configured, skipping URL refresh service');
      return;
    }

    if (this.isRunning) {
      console.log('[URLRefreshService] Service already running');
      return;
    }

    // Run every 6 hours: 0 */6 * * *
    // For testing, you can use '*/10 * * * *' (every 10 minutes)
    this.cronJob = cron.schedule('0 */6 * * *', async () => {
      await this.refreshExpiredUrls();
    }, {
      scheduled: false,
      timezone: 'UTC'
    });

    this.cronJob.start();
    this.isRunning = true;
    
    console.log('[URLRefreshService] Started - will check for expiring URLs every 6 hours');
    
    // Run initial check after 1 minute
    setTimeout(() => {
      this.refreshExpiredUrls();
    }, 60000);
  }

  /**
   * Stop the URL refresh service
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
    }
    this.isRunning = false;
    console.log('[URLRefreshService] Stopped');
  }

  /**
   * Get service status and statistics
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      stats: this.refreshStats,
      nextRun: this.cronJob ? this.cronJob.nextDate() : null
    };
  }

  /**
   * Find content with URLs that need refreshing
   */
  async findContentNeedingRefresh(skip = 0, limit = this.batchSize) {
    try {
      const now = new Date();
      const refreshThreshold = new Date(now.getTime() - (S3_URL_REFRESH_THRESHOLD * 1000));

      // Find content where URLs were generated more than threshold time ago
      // or where generation timestamps are missing
      const query = {
        $and: [
          { isActive: 1 },
          { status: 'Published' },
          {
            $or: [
              // File URL needs refresh
              {
                $and: [
                  { fileUrl: { $regex: /amazonaws\.com/ } },
                  {
                    $or: [
                      { fileUrlGeneratedAt: { $exists: false } },
                      { fileUrlGeneratedAt: null },
                      { fileUrlGeneratedAt: { $lt: refreshThreshold } }
                    ]
                  }
                ]
              },
              // Preview URL needs refresh
              {
                $and: [
                  { previewUrl: { $regex: /amazonaws\.com/ } },
                  {
                    $or: [
                      { previewUrlGeneratedAt: { $exists: false } },
                      { previewUrlGeneratedAt: null },
                      { previewUrlGeneratedAt: { $lt: refreshThreshold } }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      };

      const content = await Content.find(query)
        .select('_id title fileUrl fileUrlGeneratedAt previewUrl previewUrlGeneratedAt contentType')
        .skip(skip)
        .limit(limit)
        .lean();

      return content;
    } catch (error) {
      console.error('[URLRefreshService] Error finding content needing refresh:', error);
      return [];
    }
  }

  /**
   * Refresh URLs for a batch of content items
   */
  async refreshContentBatch(contentItems) {
    const results = {
      processed: 0,
      refreshed: 0,
      errors: 0
    };

    for (const content of contentItems) {
      try {
        results.processed++;
        
        // Check if this content actually needs URL refresh
        const needsFileRefresh = content.fileUrl && needsUrlRefresh(content.fileUrlGeneratedAt);
        const needsPreviewRefresh = content.previewUrl && needsUrlRefresh(content.previewUrlGeneratedAt);
        
        if (needsFileRefresh || needsPreviewRefresh) {
          console.log(`[URLRefreshService] Refreshing URLs for content: ${content._id} (${content.title})`);
          
          // Process and update URLs
          await processContentUrls(content, true);
          results.refreshed++;
          
          console.log(`[URLRefreshService] Successfully refreshed URLs for: ${content._id}`);
        }
      } catch (error) {
        console.error(`[URLRefreshService] Error refreshing URLs for content ${content._id}:`, error);
        results.errors++;
      }
    }

    return results;
  }

  /**
   * Main method to refresh expired URLs
   */
  async refreshExpiredUrls() {
    if (!isUsingS3Storage()) {
      return;
    }

    console.log('[URLRefreshService] Starting URL refresh check...');
    const startTime = new Date();
    
    let totalProcessed = 0;
    let totalRefreshed = 0;
    let totalErrors = 0;
    let skip = 0;
    let hasMore = true;

    try {
      while (hasMore) {
        // Find content that needs URL refresh
        const contentItems = await this.findContentNeedingRefresh(skip, this.batchSize);
        
        if (contentItems.length === 0) {
          hasMore = false;
          break;
        }

        console.log(`[URLRefreshService] Processing batch of ${contentItems.length} content items (skip: ${skip})`);
        
        // Refresh URLs for this batch
        const batchResults = await this.refreshContentBatch(contentItems);
        
        totalProcessed += batchResults.processed;
        totalRefreshed += batchResults.refreshed;
        totalErrors += batchResults.errors;
        
        skip += this.batchSize;
        
        // If we got fewer items than batch size, we're done
        if (contentItems.length < this.batchSize) {
          hasMore = false;
        }

        // Add a small delay between batches to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Update statistics
      this.refreshStats = {
        totalProcessed: totalProcessed,
        urlsRefreshed: totalRefreshed,
        errors: totalErrors,
        lastRun: new Date(),
        duration: new Date() - startTime
      };

      console.log(`[URLRefreshService] Completed URL refresh check:`, this.refreshStats);
      
    } catch (error) {
      console.error('[URLRefreshService] Error during URL refresh process:', error);
      this.refreshStats.errors++;
    }
  }

  /**
   * Manually trigger URL refresh (for testing or admin use)
   */
  async triggerRefresh() {
    console.log('[URLRefreshService] Manual refresh triggered');
    await this.refreshExpiredUrls();
  }
}

// Create singleton instance
const urlRefreshService = new UrlRefreshService();

module.exports = urlRefreshService;
