/**
 * Migration script to add URL generation timestamps to existing content
 * This script updates existing content records to include fileUrlGeneratedAt and previewUrlGeneratedAt
 * for S3 files that don't have these timestamps yet.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Content = require('../models/Content');
const { isS3Url } = require('../utils/storageHelper');

// Load environment variables
dotenv.config();

/**
 * Connect to MongoDB
 */
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI);
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

/**
 * Update content records with missing URL timestamps
 */
const migrateUrlTimestamps = async () => {
  try {
    console.log('🔄 Starting URL timestamp migration...');
    
    // Find all content with S3 URLs but missing timestamps
    const query = {
      $or: [
        // File URL is S3 but missing timestamp
        {
          $and: [
            { fileUrl: { $regex: /amazonaws\.com/ } },
            {
              $or: [
                { fileUrlGeneratedAt: { $exists: false } },
                { fileUrlGeneratedAt: null }
              ]
            }
          ]
        },
        // Preview URL is S3 but missing timestamp
        {
          $and: [
            { previewUrl: { $regex: /amazonaws\.com/ } },
            {
              $or: [
                { previewUrlGeneratedAt: { $exists: false } },
                { previewUrlGeneratedAt: null }
              ]
            }
          ]
        }
      ]
    };

    const contentToUpdate = await Content.find(query).select('_id title fileUrl previewUrl fileUrlGeneratedAt previewUrlGeneratedAt createdAt');
    
    console.log(`📊 Found ${contentToUpdate.length} content items that need URL timestamp migration`);
    
    if (contentToUpdate.length === 0) {
      console.log('✅ No content needs URL timestamp migration');
      return;
    }

    let updated = 0;
    let errors = 0;

    for (const content of contentToUpdate) {
      try {
        const updateData = {};
        let needsUpdate = false;

        // Set file URL timestamp if missing
        if (content.fileUrl && isS3Url(content.fileUrl) && !content.fileUrlGeneratedAt) {
          // Use creation date as fallback timestamp (URLs would have been generated when content was created)
          updateData.fileUrlGeneratedAt = content.createdAt || new Date();
          needsUpdate = true;
          console.log(`📝 Setting fileUrlGeneratedAt for content: ${content._id} (${content.title})`);
        }

        // Set preview URL timestamp if missing
        if (content.previewUrl && isS3Url(content.previewUrl) && !content.previewUrlGeneratedAt) {
          // Use creation date as fallback timestamp
          updateData.previewUrlGeneratedAt = content.createdAt || new Date();
          needsUpdate = true;
          console.log(`📝 Setting previewUrlGeneratedAt for content: ${content._id} (${content.title})`);
        }

        if (needsUpdate) {
          await Content.findByIdAndUpdate(content._id, updateData);
          updated++;
          console.log(`✅ Updated content: ${content._id}`);
        }
      } catch (error) {
        console.error(`❌ Error updating content ${content._id}:`, error);
        errors++;
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`   Total content checked: ${contentToUpdate.length}`);
    console.log(`   Successfully updated: ${updated}`);
    console.log(`   Errors: ${errors}`);
    
    if (errors === 0) {
      console.log('✅ URL timestamp migration completed successfully!');
    } else {
      console.log(`⚠️  URL timestamp migration completed with ${errors} errors`);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

/**
 * Verify migration results
 */
const verifyMigration = async () => {
  try {
    console.log('\n🔍 Verifying migration results...');
    
    // Count content with S3 URLs but missing timestamps
    const stillMissingQuery = {
      $or: [
        {
          $and: [
            { fileUrl: { $regex: /amazonaws\.com/ } },
            {
              $or: [
                { fileUrlGeneratedAt: { $exists: false } },
                { fileUrlGeneratedAt: null }
              ]
            }
          ]
        },
        {
          $and: [
            { previewUrl: { $regex: /amazonaws\.com/ } },
            {
              $or: [
                { previewUrlGeneratedAt: { $exists: false } },
                { previewUrlGeneratedAt: null }
              ]
            }
          ]
        }
      ]
    };

    const stillMissing = await Content.countDocuments(stillMissingQuery);
    
    if (stillMissing === 0) {
      console.log('✅ Verification passed: All S3 URLs now have timestamps');
    } else {
      console.log(`⚠️  Verification warning: ${stillMissing} content items still missing timestamps`);
    }

    // Count total S3 content
    const totalS3Content = await Content.countDocuments({
      $or: [
        { fileUrl: { $regex: /amazonaws\.com/ } },
        { previewUrl: { $regex: /amazonaws\.com/ } }
      ]
    });

    console.log(`📊 Total content with S3 URLs: ${totalS3Content}`);
    console.log(`📊 Content still missing timestamps: ${stillMissing}`);
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
};

/**
 * Main migration function
 */
const runMigration = async () => {
  try {
    await connectDB();
    await migrateUrlTimestamps();
    await verifyMigration();
    
    console.log('\n🎉 Migration process completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration process failed:', error);
    process.exit(1);
  }
};

// Run migration if this script is executed directly
if (require.main === module) {
  console.log('🚀 Starting URL timestamp migration script...');
  runMigration();
}

module.exports = {
  migrateUrlTimestamps,
  verifyMigration,
  runMigration
};
