const { processContentUrls } = require('../utils/urlManager');
const { isUsingS3Storage } = require('../utils/storageHelper');

/**
 * Middleware to automatically refresh expired S3 URLs in content responses
 * This middleware processes content objects and refreshes any expired signed URLs
 */
const refreshContentUrls = (req, res, next) => {
  // Only apply this middleware if we're using S3 storage
  if (!isUsingS3Storage()) {
    return next();
  }

  // Store the original json method
  const originalJson = res.json;

  // Override the json method
  res.json = function (data) {
    try {
      // Process the response data to refresh URLs
      processResponseData(data)
        .then(processedData => {
          return originalJson.call(this, processedData);
        })
        .catch(error => {
          console.error('[URLRefresh] Error processing response data:', error);
          // If there's an error, return the original data
          return originalJson.call(this, data);
        });
    } catch (error) {
      console.error('[URLRefresh] Error in URL refresh middleware:', error);
      // If there's an error, return the original data
      return originalJson.call(this, data);
    }
  };

  next();
};

/**
 * Recursively process response data to refresh URLs in content objects
 * @param {any} obj - The object to process
 * @param {Set} visited - Set to track visited objects to prevent infinite recursion
 * @param {number} depth - Current recursion depth
 * @param {number} maxDepth - Maximum allowed recursion depth
 * @returns {Promise<any>} - The processed object with refreshed URLs
 */
const processResponseData = async (obj, visited = new Set(), depth = 0, maxDepth = 5) => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  // Prevent infinite recursion by limiting depth
  if (depth > maxDepth) {
    console.warn(`[URLRefresh] Maximum recursion depth (${maxDepth}) reached, stopping processing`);
    return obj;
  }

  // Handle primitive types
  if (typeof obj !== 'object') {
    return obj;
  }

  // Prevent circular references
  if (visited.has(obj)) {
    return obj;
  }
  visited.add(obj);

  try {
    // Handle arrays
    if (Array.isArray(obj)) {
      const processedArray = [];
      for (const item of obj) {
        const processedItem = await processResponseData(item, visited, depth + 1, maxDepth);
        processedArray.push(processedItem);
      }
      return processedArray;
    }

    // Handle objects
    const processed = {};

    // Check if this looks like a content object (has _id, fileUrl, and contentType)
    if (obj._id && obj.fileUrl && obj.contentType) {
      console.log(`[URLRefresh] Processing content object: ${obj._id}`);
      
      // Process this content object to refresh URLs
      const refreshedContent = await processContentUrls(obj, true);
      
      // Copy all properties from refreshed content
      Object.assign(processed, refreshedContent);
      
      // Continue processing nested objects
      for (const [key, value] of Object.entries(refreshedContent)) {
        if (typeof value === 'object' && value !== null && !['fileUrlGeneratedAt', 'previewUrlGeneratedAt'].includes(key)) {
          processed[key] = await processResponseData(value, visited, depth + 1, maxDepth);
        }
      }
    } else {
      // Regular object processing
      for (const [key, value] of Object.entries(obj)) {
        processed[key] = await processResponseData(value, visited, depth + 1, maxDepth);
      }
    }

    return processed;
  } catch (error) {
    console.error('[URLRefresh] Error processing object:', error);
    return obj; // Return original object on error
  } finally {
    visited.delete(obj);
  }
};

/**
 * Middleware specifically for single content responses
 * This is more efficient for single content items
 */
const refreshSingleContentUrls = async (req, res, next) => {
  // Only apply this middleware if we're using S3 storage
  if (!isUsingS3Storage()) {
    return next();
  }

  // Store the original json method
  const originalJson = res.json;

  // Override the json method
  res.json = function (data) {
    try {
      // Check if this is a single content response
      if (data && data.success && data.data && data.data._id && data.data.fileUrl) {
        processContentUrls(data.data, true)
          .then(refreshedContent => {
            const processedData = {
              ...data,
              data: refreshedContent
            };
            return originalJson.call(this, processedData);
          })
          .catch(error => {
            console.error('[URLRefresh] Error refreshing single content URLs:', error);
            return originalJson.call(this, data);
          });
      } else {
        // Not a content response, pass through
        return originalJson.call(this, data);
      }
    } catch (error) {
      console.error('[URLRefresh] Error in single content URL refresh middleware:', error);
      return originalJson.call(this, data);
    }
  };

  next();
};

/**
 * Express route handler to manually refresh URLs for a specific content item
 */
const refreshContentUrlsHandler = async (req, res, next) => {
  try {
    const contentId = req.params.id;
    
    if (!contentId) {
      return res.status(400).json({
        success: false,
        error: 'Content ID is required'
      });
    }

    const Content = require('../models/Content');
    const content = await Content.findById(contentId);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: 'Content not found'
      });
    }

    // Process and update URLs
    const refreshedContent = await processContentUrls(content, true);

    res.status(200).json({
      success: true,
      data: refreshedContent,
      message: 'URLs refreshed successfully'
    });
  } catch (error) {
    console.error('[URLRefresh] Error in refresh handler:', error);
    next(error);
  }
};

module.exports = {
  refreshContentUrls,
  refreshSingleContentUrls,
  refreshContentUrlsHandler,
  processResponseData
};
