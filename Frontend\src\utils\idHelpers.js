/**
 * Utility functions for handling ID conversions to prevent ObjectId casting errors
 */

/**
 * Converts an ID to a string, handling ObjectId objects
 * @param {string|Object} id - The ID to convert
 * @returns {string} - The ID as a string
 */
export const ensureStringId = (id) => {
  if (!id) return '';
  if (typeof id === 'object') {
    // Handle ObjectId objects
    if (id.toString) {
      return id.toString();
    }
    // Handle plain objects that might have been stringified incorrectly
    if (id._id) {
      return typeof id._id === 'object' ? id._id.toString() : id._id;
    }
    // Fallback for other object types
    return String(id);
  }
  return String(id);
};

/**
 * Validates that an ID is not an invalid object string
 * @param {string} id - The ID to validate
 * @returns {boolean} - True if the ID is valid
 */
export const isValidId = (id) => {
  if (!id) return false;
  const stringId = String(id);
  return stringId !== '[object Object]' && 
         stringId !== 'undefined' && 
         stringId !== 'null' &&
         stringId.trim() !== '';
};

/**
 * Safely converts an ID and validates it
 * @param {string|Object} id - The ID to convert and validate
 * @returns {string|null} - The valid string ID or null if invalid
 */
export const safeConvertId = (id) => {
  const stringId = ensureStringId(id);
  return isValidId(stringId) ? stringId : null;
};
