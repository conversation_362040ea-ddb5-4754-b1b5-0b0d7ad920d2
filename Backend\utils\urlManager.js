const { getSignedUrl, isS3Url } = require('./storageHelper');
const { S3_URL_EXPIRATION, S3_PREVIEW_URL_EXPIRATION, S3_URL_REFRESH_THRESHOLD } = require('../config/timeouts');

/**
 * Extract S3 key from S3 URL
 * @param {string} s3Url - S3 URL
 * @returns {string|null} - S3 key or null if not extractable
 */
const extractS3Key = (s3Url) => {
  if (!s3Url || !isS3Url(s3Url)) {
    return null;
  }

  try {
    // Handle different S3 URL formats
    const url = new URL(s3Url);
    
    // Format: https://bucket.s3.region.amazonaws.com/key
    if (url.hostname.includes('.s3.') && url.hostname.includes('.amazonaws.com')) {
      return url.pathname.substring(1); // Remove leading slash
    }
    
    // Format: https://s3.region.amazonaws.com/bucket/key
    if (url.hostname.includes('s3.') && url.hostname.includes('.amazonaws.com')) {
      const pathParts = url.pathname.split('/');
      if (pathParts.length > 2) {
        return pathParts.slice(2).join('/'); // Remove empty string and bucket name
      }
    }
    
    return null;
  } catch (error) {
    console.error('[URLManager] Error extracting S3 key:', error);
    return null;
  }
};

/**
 * Check if a URL needs to be refreshed based on generation timestamp
 * @param {Date} generatedAt - When the URL was generated
 * @param {number} refreshThreshold - Seconds before expiration to refresh (default: 24 hours)
 * @returns {boolean} - Whether the URL needs refresh
 */
const needsUrlRefresh = (generatedAt, refreshThreshold = S3_URL_REFRESH_THRESHOLD) => {
  if (!generatedAt) {
    return true; // No generation timestamp means we should refresh
  }

  const now = new Date();
  const timeSinceGeneration = (now - generatedAt) / 1000; // Convert to seconds
  const timeUntilRefresh = S3_URL_EXPIRATION - refreshThreshold;
  
  return timeSinceGeneration >= timeUntilRefresh;
};

/**
 * Generate a fresh signed URL for S3 content
 * @param {string} s3Url - Original S3 URL
 * @param {string} urlType - Type of URL ('file' or 'preview')
 * @returns {Object} - Object containing signed URL and generation timestamp
 */
const generateFreshSignedUrl = (s3Url, urlType = 'file') => {
  if (!s3Url || !isS3Url(s3Url)) {
    return { signedUrl: s3Url, generatedAt: null };
  }

  try {
    const s3Key = extractS3Key(s3Url);
    if (!s3Key) {
      console.warn('[URLManager] Could not extract S3 key from URL:', s3Url);
      return { signedUrl: s3Url, generatedAt: null };
    }

    const expirationTime = urlType === 'preview' ? S3_PREVIEW_URL_EXPIRATION : S3_URL_EXPIRATION;
    const signedUrl = getSignedUrl(s3Key, expirationTime);
    const generatedAt = new Date();

    console.log(`[URLManager] Generated fresh ${urlType} signed URL for key: ${s3Key}`);
    
    return {
      signedUrl,
      generatedAt
    };
  } catch (error) {
    console.error('[URLManager] Error generating fresh signed URL:', error);
    return { signedUrl: s3Url, generatedAt: null };
  }
};

/**
 * Refresh URLs for content if needed
 * @param {Object} content - Content document
 * @returns {Object} - Object with refreshed URLs and update flags
 */
const refreshContentUrls = async (content) => {
  const result = {
    fileUrl: content.fileUrl,
    fileUrlGeneratedAt: content.fileUrlGeneratedAt,
    previewUrl: content.previewUrl,
    previewUrlGeneratedAt: content.previewUrlGeneratedAt,
    needsUpdate: false
  };

  // Check and refresh main file URL
  if (content.fileUrl && isS3Url(content.fileUrl)) {
    if (needsUrlRefresh(content.fileUrlGeneratedAt)) {
      const { signedUrl, generatedAt } = generateFreshSignedUrl(content.fileUrl, 'file');
      result.fileUrl = signedUrl;
      result.fileUrlGeneratedAt = generatedAt;
      result.needsUpdate = true;
      console.log(`[URLManager] Refreshed file URL for content: ${content._id}`);
    }
  }

  // Check and refresh preview URL
  if (content.previewUrl && isS3Url(content.previewUrl)) {
    if (needsUrlRefresh(content.previewUrlGeneratedAt)) {
      const { signedUrl, generatedAt } = generateFreshSignedUrl(content.previewUrl, 'preview');
      result.previewUrl = signedUrl;
      result.previewUrlGeneratedAt = generatedAt;
      result.needsUpdate = true;
      console.log(`[URLManager] Refreshed preview URL for content: ${content._id}`);
    }
  }

  return result;
};

/**
 * Update content URLs in database if they were refreshed
 * @param {Object} content - Content document
 * @param {Object} refreshResult - Result from refreshContentUrls
 * @returns {Promise<Object>} - Updated content document
 */
const updateContentUrlsInDb = async (content, refreshResult) => {
  if (!refreshResult.needsUpdate) {
    return content;
  }

  try {
    const updateData = {};
    
    if (refreshResult.fileUrl !== content.fileUrl) {
      updateData.fileUrl = refreshResult.fileUrl;
      updateData.fileUrlGeneratedAt = refreshResult.fileUrlGeneratedAt;
    }
    
    if (refreshResult.previewUrl !== content.previewUrl) {
      updateData.previewUrl = refreshResult.previewUrl;
      updateData.previewUrlGeneratedAt = refreshResult.previewUrlGeneratedAt;
    }

    if (Object.keys(updateData).length > 0) {
      const Content = require('../models/Content');
      const updatedContent = await Content.findByIdAndUpdate(
        content._id,
        updateData,
        { new: true }
      );
      
      console.log(`[URLManager] Updated content URLs in database: ${content._id}`);
      return updatedContent;
    }
    
    return content;
  } catch (error) {
    console.error('[URLManager] Error updating content URLs in database:', error);
    return content;
  }
};

/**
 * Process content and refresh URLs if needed
 * @param {Object} content - Content document
 * @param {boolean} updateDb - Whether to update the database
 * @returns {Promise<Object>} - Content with refreshed URLs
 */
const processContentUrls = async (content, updateDb = true) => {
  try {
    const refreshResult = await refreshContentUrls(content);
    
    if (refreshResult.needsUpdate && updateDb) {
      return await updateContentUrlsInDb(content, refreshResult);
    }
    
    // Return content with refreshed URLs but don't update DB
    return {
      ...content.toObject ? content.toObject() : content,
      fileUrl: refreshResult.fileUrl,
      fileUrlGeneratedAt: refreshResult.fileUrlGeneratedAt,
      previewUrl: refreshResult.previewUrl,
      previewUrlGeneratedAt: refreshResult.previewUrlGeneratedAt
    };
  } catch (error) {
    console.error('[URLManager] Error processing content URLs:', error);
    return content;
  }
};

module.exports = {
  extractS3Key,
  needsUrlRefresh,
  generateFreshSignedUrl,
  refreshContentUrls,
  updateContentUrlsInDb,
  processContentUrls
};
