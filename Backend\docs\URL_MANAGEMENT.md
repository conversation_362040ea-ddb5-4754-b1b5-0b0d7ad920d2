# URL Management System

This document describes the enhanced URL management system for XOSportsHub that provides automatic URL refresh functionality for AWS S3 signed URLs.

## Overview

The URL management system extends signed URL expiration from 24 hours to 7 days and implements automatic URL regeneration to ensure users never lose access to documents due to expired URLs.

## Key Features

### 1. Extended URL Expiration
- **File URLs**: 7 days (604,800 seconds)
- **Preview URLs**: 7 days (604,800 seconds)
- **Refresh Threshold**: 24 hours before expiration

### 2. Automatic URL Refresh
- Background service checks for expiring URLs every 6 hours
- Middleware automatically refreshes URLs in API responses
- Manual refresh endpoints for admin control

### 3. URL Generation Tracking
- Database tracks when URLs were generated (`fileUrlGeneratedAt`, `previewUrlGeneratedAt`)
- Smart refresh logic based on generation timestamps

## Architecture

### Core Components

#### 1. URL Manager (`utils/urlManager.js`)
- `generateFreshSignedUrl()` - Creates new signed URLs
- `needsUrlRefresh()` - Checks if U<PERSON> needs refreshing
- `processContentUrls()` - Processes content and refreshes URLs
- `refreshContentUrls()` - Refreshes URLs for content objects

#### 2. URL Refresh Middleware (`middleware/urlRefresh.js`)
- `refreshContentUrls` - General middleware for all responses
- `refreshSingleContentUrls` - Optimized for single content responses
- `refreshContentUrlsHandler` - Manual refresh endpoint handler

#### 3. URL Refresh Service (`services/urlRefreshService.js`)
- Background cron job (runs every 6 hours)
- Batch processing (50 items at a time)
- Proactive URL refresh before expiration
- Statistics and monitoring

#### 4. Enhanced S3 URL Handler (`middleware/s3UrlHandler.js`)
- Integrates with URL refresh system
- Fallback to original processing if refresh fails
- Processes content objects first, then remaining S3 URLs

## Configuration

### Environment Variables
```bash
# AWS Configuration (existing)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_BUCKET_NAME=your_bucket_name
AWS_REGION=your_region
```

### Timeout Configuration (`config/timeouts.js`)
```javascript
S3_URL_EXPIRATION: 7 * 24 * 60 * 60, // 7 days
S3_PREVIEW_URL_EXPIRATION: 7 * 24 * 60 * 60, // 7 days
S3_URL_REFRESH_THRESHOLD: 24 * 60 * 60, // 24 hours
```

## Database Schema Changes

### Content Model Updates
```javascript
// New fields added to Content schema
fileUrlGeneratedAt: {
  type: Date,
  index: true,
},
previewUrlGeneratedAt: {
  type: Date,
  index: true,
}
```

## API Endpoints

### Content URL Refresh
```
POST /api/content/:id/refresh-urls
Authorization: Bearer token (admin/seller)
```

### Diagnostics
```
GET /api/diagnostics/url-refresh-status
Authorization: Bearer token (admin)

POST /api/diagnostics/trigger-url-refresh
Authorization: Bearer token (admin)
```

## Usage

### Automatic Operation
The system works automatically once deployed:

1. **Content Creation**: URLs get generation timestamps
2. **API Responses**: Middleware automatically refreshes expired URLs
3. **Background Service**: Proactively refreshes URLs every 6 hours
4. **Database Updates**: Refreshed URLs are saved with new timestamps

### Manual Operations

#### Check Service Status
```bash
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  http://localhost:5000/api/diagnostics/url-refresh-status
```

#### Trigger Manual Refresh
```bash
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  http://localhost:5000/api/diagnostics/trigger-url-refresh
```

#### Refresh Specific Content
```bash
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/content/CONTENT_ID/refresh-urls
```

## Migration

### Existing Content Migration
Run the migration script to add timestamps to existing content:

```bash
cd Backend
node scripts/migrate-url-timestamps.js
```

This script:
- Finds content with S3 URLs but missing timestamps
- Sets timestamps using content creation date as fallback
- Verifies migration results

## Monitoring

### Service Statistics
The URL refresh service tracks:
- Total content processed
- URLs refreshed
- Errors encountered
- Last run timestamp
- Processing duration

### Logs
Monitor these log patterns:
- `[URLManager]` - URL generation and refresh operations
- `[URLRefreshService]` - Background service operations
- `[S3UrlHandler]` - Middleware URL processing

## Error Handling

### Graceful Degradation
- If URL refresh fails, original URLs are returned
- Service continues operating even if some URLs fail to refresh
- Fallback to original S3 URL handler if enhanced processing fails

### Error Recovery
- Automatic retry logic in background service
- Individual content failures don't stop batch processing
- Detailed error logging for troubleshooting

## Performance Considerations

### Batch Processing
- Background service processes 50 items at a time
- 1-second delay between batches to avoid overwhelming system
- Efficient database queries with proper indexing

### Middleware Optimization
- Recursive processing with depth limits
- Circular reference protection
- Selective processing of content objects only

### Database Indexing
```javascript
// Recommended indexes
{ fileUrlGeneratedAt: 1 }
{ previewUrlGeneratedAt: 1 }
{ fileUrl: 1, fileUrlGeneratedAt: 1 }
{ previewUrl: 1, previewUrlGeneratedAt: 1 }
```

## Security

### Access Control
- URL refresh endpoints require authentication
- Admin-only access for service management
- Seller access for own content refresh

### URL Security
- Signed URLs maintain same security as before
- Extended expiration doesn't compromise security
- URLs are regenerated with fresh signatures

## Troubleshooting

### Common Issues

#### URLs Still Expiring
1. Check if S3 credentials are configured
2. Verify URL refresh service is running
3. Check for errors in service logs

#### Service Not Starting
1. Verify MongoDB connection
2. Check S3 configuration
3. Review server startup logs

#### Performance Issues
1. Monitor batch processing size
2. Check database query performance
3. Review middleware processing depth

### Debug Commands
```bash
# Check service status
curl -H "Authorization: Bearer TOKEN" localhost:5000/api/diagnostics/url-refresh-status

# Trigger manual refresh
curl -X POST -H "Authorization: Bearer TOKEN" localhost:5000/api/diagnostics/trigger-url-refresh

# Check specific content
curl -H "Authorization: Bearer TOKEN" localhost:5000/api/content/CONTENT_ID
```

## Future Enhancements

### Planned Features
- Configurable refresh schedules
- URL expiration notifications
- Advanced monitoring dashboard
- Bulk URL refresh operations
- URL health checks

### Optimization Opportunities
- Predictive URL refresh based on access patterns
- Caching layer for frequently accessed URLs
- Distributed URL refresh for high-scale deployments
