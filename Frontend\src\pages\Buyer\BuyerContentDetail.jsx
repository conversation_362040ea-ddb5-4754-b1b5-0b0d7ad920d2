import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, <PERSON> } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { getContent } from "../../redux/slices/contentSlice";
import { createOrder } from "../../redux/slices/orderSlice";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import StrategyCard from "../../components/common/StrategyCard";
import DocumentViewer from "../../components/common/DocumentViewer";
import PreviewModal from "../../components/common/PreviewModal";
import {
  MdStar,
  MdStarBorder,
  MdVisibility,
} from "react-icons/md";

import "../../styles/ItemDetail.css";
import { getImageUrl, IMAGE_BASE_URL, VALIDATION } from "../../utils/constants";
import BidModal from "../../components/common/BidModal";
import OfferModal from "../../components/common/OfferModal";
import RequestCustomTrainingModal from "../../components/common/RequestCustomTrainingModal";
import CountdownTimer from "../../components/common/CountdownTimer";
import { toast } from "react-toastify";
import PreviewContent from "../../components/common/PreviewContent";
import api from "../../services/api";
import { CONTENT_ENDPOINTS } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import { ensureStringId } from "../../utils/idHelpers";
import RatingStars from "../../components/common/RatingStars";







const ContentDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("description");
  const [isBidModalOpen, setIsBidModalOpen] = useState(false);
  const [isOfferModalOpen, setIsOfferModalOpen] = useState(false);
  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [showMoreDesc, setShowMoreDesc] = useState(false);
  const [showMoreCoach, setShowMoreCoach] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [relatedStrategies, setRelatedStrategies] = useState([]);
  const [loadingRelated, setLoadingRelated] = useState(true);
  const [reviews, setReviews] = useState([]);
  const [userReview, setUserReview] = useState(null);
  const [reviewFormData, setReviewFormData] = useState({
    rating: 5,
    title: "",
    text: "",
  });
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [reviewError, setReviewError] = useState(null);

  // Redux selectors
  const { user } = useSelector((state) => state.auth);
  const { isLoading: orderLoading } = useSelector((state) => state.order);

  useEffect(() => {
    setLoading(true);
    dispatch(getContent(id))
      .unwrap()
      .then((res) => {
        const data = res.data;
        const formatted = {
          ...data,
          coach: data.coachName,
          coachBio: data.aboutCoach,
          fileUrl: data.fileUrl,
          includes: data.strategicContent,
        };
        setContent(formatted);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message || "Failed to load content details");
        setLoading(false);
      });
  }, [id, dispatch]);

  // Fetch related strategies
  useEffect(() => {
    const fetchRelatedStrategies = async () => {
      try {
        setLoadingRelated(true);
        const response = await api.get(CONTENT_ENDPOINTS.ALL, {
          params: {
            sport: content.sport || content.category, // Try both sport and category fields
            isActive: 1,
            limit: 4,
            page: 1,
            //excludeId: content._id // Exclude current content
          },
        });

        // Filter out the current content and take up to 4 items
        const filteredStrategies = (response.data.data || [])
          .filter((strategy) => strategy._id !== content._id)
          .slice(0, 4);

        setRelatedStrategies(filteredStrategies);
      } catch (error) {
        console.error("Error fetching related strategies:", error);
        setRelatedStrategies([]);
      } finally {
        setLoadingRelated(false);
      }
    };

    if (content?.sport || content?.category) {
      fetchRelatedStrategies();
    }
  }, [content?._id, content?.sport, content?.category]);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        // Only fetch reviews when we have the content object
        if (content?._id) {
          // Ensure _id is converted to string to avoid ObjectId casting errors
          const contentId = ensureStringId(content._id);
          const response = await api.get(`/reviews/content/${contentId}`);
          setReviews(response.data.data);
          setLoadingReviews(false);
        }
      } catch (error) {
        setReviewError(
          error.response?.data?.message || "Failed to fetch reviews"
        );
        setLoadingReviews(false);
      }
    };

    fetchReviews();
  }, [content?._id]); // Depend on content._id instead of URL id

  // Add isOwner check function
  const isOwner = () => {
    if (!user || !content) return false;
    return content.seller._id === user._id;
  };

  // Modify handleBuyNow
  const handleBuyNow = async () => {
    // Check if user is owner
    if (isOwner()) {
      toast.error("You can't buy your own content.");
      return;
    }

    // Check if user is logged in
    if (!user) {
      toast.error("Please log in to purchase content");
      navigate("/login");
      return;
    }

    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole =
      user.role === "admin" ? user.role : user.activeRole || user.role;
    if (effectiveRole !== "buyer" && user.role !== "admin") {
      toast.error("Only buyers can purchase content");
      return;
    }

    // Validate content ID
    if (!id || id === "undefined") {
      toast.error("Invalid content ID. Please try again.");
      return;
    }

    setIsCreatingOrder(true);

    try {
      // Create order for fixed price content
      const orderData = {
        contentId: id,
        orderType: "Fixed",
      };

      const result = await dispatch(createOrder(orderData)).unwrap();

      // Extract order data from response
      const createdOrder = result.data || result;

      // Validate the created order
      if (!createdOrder || !createdOrder._id) {
        console.error("Order creation response:", result);
        throw new Error("Order creation failed - no order ID returned");
      }

      // Validate order ID format using utility function
      if (!VALIDATION.isValidObjectId(createdOrder._id)) {
        throw new Error("Invalid order ID format returned from server");
      }

      // Navigate to checkout with order ID
      navigate(`/checkout/${createdOrder._id}`);
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error(error.message || "Failed to create order. Please try again.");
    } finally {
      setIsCreatingOrder(false);
    }
  };

  // Add handler for bid button click
  const handleBidClick = () => {
    if (isOwner()) {
      toast.error("You can't bid on your own content.");
      return;
    }
    setIsBidModalOpen(true);
  };

  // Add handler for offer button click
  const handleOfferClick = () => {
    if (isOwner()) {
      toast.error("You can't make an offer on your own content.");
      return;
    }
    setIsOfferModalOpen(true);
  };

  // Function to determine auction status and button type
  const getAuctionStatus = () => {
    if (!content || content.saleType !== "Auction") {
      return { status: "not_auction", buttonType: "buy" };
    }

    const now = new Date();
    const startDate = content.auctionDetails?.auctionStartDate
      ? new Date(content.auctionDetails.auctionStartDate)
      : null;
    const endDate = content.auctionDetails?.auctionEndDate
      ? new Date(content.auctionDetails.auctionEndDate)
      : null;

    // Check if content is sold
    if (content.isSold) {
      return { status: "sold", buttonType: "sold" };
    }

    // Check if auction has been ended by bid acceptance
    if (content.auctionStatus === "Ended") {
      return { status: "ended", buttonType: "ended" };
    }

    // Before auction starts
    if (startDate && now < startDate) {
      if (content.auctionDetails?.allowOfferBeforeAuctionStart) {
        return { status: "before_start_offers_allowed", buttonType: "offer" };
      } else {
        return { status: "before_start_no_offers", buttonType: "pending" };
      }
    }

    // During auction
    if (startDate && endDate && now >= startDate && now <= endDate) {
      return { status: "active", buttonType: "bid" };
    }

    // After auction time expires
    if (endDate && now > endDate) {
      return { status: "ended", buttonType: "ended" };
    }

    // Default for auction without specific dates (assume active)
    return { status: "active", buttonType: "bid" };
  };

  const auctionStatus = getAuctionStatus();

  const handleReviewSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await api.post("/reviews", {
        content: id,
        ...reviewFormData,
      });

      setReviews([response.data.data, ...reviews]);
      setUserReview(response.data.data);
      setReviewFormData({ rating: 5, title: "", text: "" });
      toast.success("Review submitted successfully!");
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to submit review");
    }
  };

  const ReviewForm = () => (
    <form onSubmit={handleReviewSubmit} className="review-form">
      <div className="rating-input">
        <label>Rating:</label>
        <div className="stars">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() =>
                setReviewFormData({ ...reviewFormData, rating: star })
              }
              className="star-button"
            >
              {star <= reviewFormData.rating ? <MdStar /> : <MdStarBorder />}
            </button>
          ))}
        </div>
      </div>
      <div className="form-group">
        <input
          type="text"
          placeholder="Review Title"
          value={reviewFormData.title}
          onChange={(e) =>
            setReviewFormData({ ...reviewFormData, title: e.target.value })
          }
          required
          maxLength="100"
          className="review-input"
        />
      </div>
      <div className="form-group">
        <textarea
          placeholder="Write your review..."
          value={reviewFormData.text}
          onChange={(e) =>
            setReviewFormData({ ...reviewFormData, text: e.target.value })
          }
          required
          maxLength="500"
          className="review-textarea"
        />
      </div>
      <button type="submit" className="submit-review-btn">
        Submit Review
      </button>
    </form>
  );

  const ReviewList = () => (
    <div className="reviews-list">
      {reviews.map((review) => (
        <div key={review._id} className="review-item">
          <div className="review-header">
            <div className="review-rating">
              <RatingStars rating={review.rating} size={20} />
            </div>
            <div className="review-title">{review.title}</div>
            <div className="review-date">
              {formatStandardDate(review.createdAt)}
            </div>
          </div>
          <div className="review-text">
            <PreviewContent html={review.text} previewLines={4} />
          </div>
          <div className="review-author">
            By {review.user.firstName} {review.user.lastName}
            {review.isVerifiedPurchase && (
              <span className="verified-badge">✅ </span>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const ReviewsTab = () => {
    const INITIAL_COUNT = 3;
    const INCREMENT = 5;
    const [visibleCount, setVisibleCount] = useState(INITIAL_COUNT);

    useEffect(() => {
      // Reset visibleCount if reviews change (e.g., new review submitted)
      setVisibleCount(INITIAL_COUNT);
    }, [reviews.length]);

    if (loadingReviews) {
      return <div className="loading-reviews">Loading reviews...</div>;
    }

    if (reviewError) {
      return <div className="review-error">{reviewError}</div>;
    }

    if (!reviews.length) {
      return <div className="no-reviews">No reviews yet</div>;
    }

    const canShowMore = visibleCount < reviews.length;
    const canShowLess = reviews.length > INITIAL_COUNT && visibleCount > INITIAL_COUNT;

    return (
      <div className="reviews-panel">
        <div className="reviews-summary">
          <h3>Customer Reviews</h3>
          <div className="average-rating">
            <RatingStars rating={content.averageRating || 0} size={28} />
            <span className="rating-number">
              {content.averageRating ? content.averageRating.toFixed(1) : "0"} out of 5
            </span>
            <span className="total-reviews">
              ({reviews.length} {reviews.length === 1 ? "review" : "reviews"})
            </span>
          </div>
        </div>

        <div className="reviews-list">
          {reviews.slice(0, visibleCount).map((review) => (
            <div key={review._id} className="review-item">
              <div className="review-text">
                <PreviewContent html={review.text} previewLines={4} />
              </div>
              <div className="review-header">
                <div className="review-rating">
                  <RatingStars rating={review.rating} size={20} />
                </div>
                <div className="review-title">{review.title}</div>
                <div className="review-date">
                  {formatStandardDate(review.createdAt)}
                </div>
              </div>
              <div className="review-author">
                By {review.user.firstName} {review.user.lastName}
                {review.isVerifiedPurchase && (
                  <span className="verified-badge">✅</span>
                )}
              </div>
            </div>
          ))}
        </div>
        <div className="reviews-pagination-controls flex justify-start items-center gap-10">
          {canShowMore && (
            <button className="preview-content-toggle" onClick={() => setVisibleCount((c) => Math.min(c + INCREMENT, reviews.length))}>
              See more
            </button>
          )}
          {canShowLess && (
            <button className="preview-content-toggle" onClick={() => setVisibleCount(INITIAL_COUNT)}>
              See less
            </button>
          )}
        </div>
      </div>
    );
  };

  if (loading) return <LoadingSkeleton type="content-detail" />;
  if (error)
    return (
      <ErrorDisplay
        title="Error Loading Content"
        message={error}
        onRetry={() => window.location.reload()}
      />
    );
  if (!content)
    return <div className="content-not-found">Content not found</div>;

  return (
    <div className="ItemDetail BuyerContentDetail">
      <div className="ItemDetail__container max-container">
        <div className="ItemDetail__content">
          <div className="ItemDetail__mainContent">
            <h1 className="ItemDetail__title">{content.title}</h1>
            <p className="ItemDetail__coach">By {content.coach}</p>

            <div className="ItemDetail__imageContainer">
              <div className="ItemDetail__contentPreview">
                <div className="ItemDetail__previewHeader">
                  <h3 className="ItemDetail__previewTitle">Content Preview</h3>
                  {(content.previewUrl || content.fileUrl) && (
                    <button
                      className="btn-outline ItemDetail__previewBtn"
                      onClick={() => setIsPreviewModalOpen(true)}
                      title="Preview Document/Video in Full Screen"
                    >
                      <MdVisibility />
                      Preview
                    </button>
                  )}
                </div>
                {content.contentType === "Video" ? (
                  <div className="ItemDetail__videoWrapper">
                    {content.previewUrl ? (
                      <video
                        className="ItemDetail__videoPreview"
                        controls
                        muted
                        onContextMenu={(e) => e.preventDefault()}
                        controlsList="nodownload noremoteplayback"
                        disablePictureInPicture
                        preload="metadata"
                        onLoadedMetadata={(e) =>
                          (e.currentTarget.currentTime = 0)
                        }
                        onTimeUpdate={(e) => {
                          if (e.currentTarget.currentTime > 10)
                            e.currentTarget.pause();
                        }}
                      >
                        <source
                          src={`${content.previewUrl.startsWith("/uploads")
                            ? IMAGE_BASE_URL + content.previewUrl
                            : content.previewUrl
                            }`}
                          type="video/mp4"
                        />
                        Your browser does not support the video tag.
                      </video>
                    ) : (
                      <div className="ItemDetail__noPreview">
                        <p>
                          Preview not available. Purchase to access full
                          content.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="ItemDetail__documentWrapper">
                    {content.previewUrl ? (
                      <div className="ItemDetail__documentPreview">
                        <DocumentViewer
                          fileUrl={
                            content.previewUrl.startsWith("/uploads")
                              ? IMAGE_BASE_URL + content.previewUrl
                              : content.previewUrl
                          }
                          fileName={(() => {
                            // Try to get filename from available URLs
                            // Priority: fileUrl > accessibleFileUrl > previewUrl
                            const fileUrlName = content.fileUrl
                              ?.split("/")
                              .pop();
                            const accessibleFileUrlName =
                              content.accessibleFileUrl?.split("/").pop();
                            const previewUrlName = content.previewUrl
                              ?.split("/")
                              .pop();

                            // Use fileUrl if available (user has full access)
                            if (
                              fileUrlName &&
                              !fileUrlName.includes("_preview")
                            ) {
                              return fileUrlName;
                            }

                            // Use accessibleFileUrl if available
                            if (
                              accessibleFileUrlName &&
                              !accessibleFileUrlName.includes("_preview")
                            ) {
                              return accessibleFileUrlName;
                            }

                            // For PDF preview files, try to extract original filename
                            if (
                              previewUrlName &&
                              (previewUrlName.includes("preview.pdf") || previewUrlName.includes("_preview.pdf"))
                            ) {
                              // Pattern: timestamp-originalname_preview.pdf
                              // Extract the original filename part
                              const match = previewUrlName.match(
                                /^\d+-(.+)_preview\.pdf$/
                              );
                              if (match) {
                                // Reconstruct the original filename as PDF
                                const originalPart = match[1];
                                return originalPart + ".pdf";
                              }
                            }

                            // Use previewUrl and clean up the name
                            if (previewUrlName) {
                              // Remove _preview suffix if present
                              return previewUrlName.replace("_preview", "");
                            }

                            // Default to PDF since we only support PDF documents
                            return "document.pdf";
                          })()}
                          title="Document Preview"
                          className="ItemDetail__documentPreview"
                          height="400px"
                          showDownload={false}
                        />
                      </div>
                    ) : (
                      <div className="ItemDetail__noPreview">
                        <p>
                          Preview not available. Purchase to access full
                          content.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="ItemDetail__tabs">
              <div className="ItemDetail__tabButtons">
                {["description", "coach", "reviews"].map((tab) => (
                  <button
                    key={tab}
                    className={`ItemDetail__tabButton ${activeTab === tab ? "ItemDetail__tabButton--active" : ""
                      }`}
                    onClick={() => setActiveTab(tab)}
                  >
                    {tab === "description"
                      ? "Description"
                      : tab === "coach"
                        ? "The Coach"
                        : "Reviews"}
                  </button>
                ))}
              </div>
              <div className="ItemDetail__tabContent">
                {activeTab === "description" && (
                  <div className="ItemDetail__tabPanel">
                    <PreviewContent
                      html={content.description}
                      className="ItemDetail__description"
                      ariaLabel="Content description preview"
                    />
                  </div>
                )}
                {activeTab === "coach" && (
                  <div className="ItemDetail__tabPanel">
                    <PreviewContent
                      html={
                        content.coachBio || "<p>Coach info not available</p>"
                      }
                      className="ItemDetail__description"
                      ariaLabel="Coach description preview"
                    />
                  </div>
                )}

                {activeTab === "reviews" && (
                  <div className="ItemDetail__tabPanel reviews-panel">
                    <ReviewsTab />
                  </div>
                )}
              </div>
            </div>
          </div>

          <aside className="ItemDetail__sidebar">
            <div className="ItemDetail__priceBox">
              <div className="ItemDetail__price">
                <p>
                  Price{" "}
                  {content.saleType === "Auction" &&
                    content.auctionDetails?.basePrice
                    ? `Starting: $${content.auctionDetails.basePrice.toFixed(
                      2
                    )}`
                    : `$${content.price?.toFixed(2) || "N/A"}`}
                </p>
              </div>

              {/* Dynamic button based on sale type and auction status */}
              {content.saleType === "Fixed" && (
                <button
                  className={`ItemDetail__buyButton btn-primary ${isOwner() ? 'disabled' : ''}`}
                  onClick={handleBuyNow}
                >
                  {isCreatingOrder || orderLoading
                    ? "Creating Order..."
                    : "Buy Now"}
                </button>
              )}

              {content.saleType === "Auction" && (
                <>
                  {/* Show countdown timer when auction hasn't started yet */}
                  {auctionStatus.status === "before_start_offers_allowed" ||
                    auctionStatus.status === "before_start_no_offers" ? (
                    <CountdownTimer
                      targetDate={content.auctionDetails?.auctionStartDate}
                      prefix="Auction starts in:"
                      size="small"
                      onComplete={() => window.location.reload()}
                    />

                  ) : null}
                  {auctionStatus.status === "before_start_offers_allowed" ||
                    auctionStatus.status === "before_start_no_offers" ? (
                    <CountdownTimer
                      targetDate={content.auctionDetails?.auctionEndDate}
                      prefix="Auction ends in:"
                      size="small"
                      onComplete={() => window.location.reload()}
                    />

                  ) : null}

                  {/* Show auction end countdown during bidding */}
                  {auctionStatus.status === "active" && (
                    <CountdownTimer
                      targetDate={content.auctionDetails?.auctionEndDate}
                      prefix="Auction ends in:"
                      size="small"
                      onComplete={() => window.location.reload()}
                    />
                  )}

                  {auctionStatus.buttonType === "offer" && (
                    <button
                      className={`ItemDetail__buyButton btn-primary ${isOwner() ? 'disabled' : ''}`}
                      onClick={handleOfferClick}

                    >
                      Make Offer
                    </button>
                  )}

                  {auctionStatus.buttonType === "bid" && (


                    <button
                      className={`ItemDetail__buyButton btn-primary ${isOwner() ? 'disabled' : ''}`}
                      onClick={handleBidClick}

                    >
                      Bid Now
                    </button>
                  )}

                  {auctionStatus.buttonType === "sold" && (
                    <button
                      className="ItemDetail__soldButton btn-primary"
                      disabled
                      style={{ marginTop: "8px" }}
                    >
                      SOLD
                    </button>
                  )}

                  {auctionStatus.buttonType === "ended" && (
                    <button
                      className="ItemDetail__endedButton btn-primary"
                      disabled
                      style={{ marginTop: "8px" }}
                    >
                      Auction Ended
                    </button>
                  )}

                  {auctionStatus.buttonType === "pending" && (
                    <button
                      className="ItemDetail__pendingButton btn-secondary"
                      disabled
                      style={{ marginTop: "8px" }}
                    >
                      Auction Starts Soon
                    </button>
                  )}
                </>
              )}

              {content.allowCustomRequests && (
                <button
                  className="ItemDetail__Request_Custom_Training btn-outline"
                  onClick={() => setIsRequestModalOpen(true)}
                >
                  Request Custom Training
                </button>
              )}
            </div>

            <div className="ItemDetail__contentIncludes">
              <h3 className="ItemDetail__sidebarTitle">Strategic Content</h3>
              <div className="ItemDetail__includesList">
                {content.includes ? (
                  <PreviewContent
                    html={content.includes}
                    className="ItemDetail__strategicContent"
                    ariaLabel="Strategic content preview"
                  />
                ) : (
                  <p className="ItemDetail__noContent">
                    No strategic content available
                  </p>
                )}
              </div>
            </div>

            <div className="ItemDetail__contentIncludes">
              <h3 className="ItemDetail__sidebarTitle">Tags</h3>
              <div className="ItemDetail__includesList">
                {content.tags ? (
                  <div className="content-tags">
                    {content.tags.map((tag, index) => (
                      <span key={index} className="tag">
                        {tag}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="ItemDetail__noContent">
                    No strategic content available
                  </p>
                )}
              </div>
            </div>

            <div className="ItemDetail__contentInfo">
              <h3 className="ItemDetail__sidebarTitle">
                Strategic Content Info
              </h3>
              <div className="ItemDetail__infoList">
                <div className="ItemDetail__infoItem">
                  <span className="ItemDetail__infoLabel">Category:</span>
                  <span className="ItemDetail__infoValue">
                    {content.category}
                  </span>
                </div>
                {content.bookings && (
                  <div className="ItemDetail__infoItem">
                    <span className="ItemDetail__infoLabel">Bookings:</span>
                    <span className="ItemDetail__infoValue">
                      {content.bookings}
                    </span>
                  </div>
                )}
                {content.duration && (
                  <div className="ItemDetail__infoItem">
                    <span className="ItemDetail__infoLabel">Duration:</span>
                    <span className="ItemDetail__infoValue">
                      {content.duration}
                    </span>
                  </div>
                )}
                <div className="ItemDetail__infoItem">
                  <span className="ItemDetail__infoLabel">Type:</span>
                  <span className="ItemDetail__infoValue">
                    {content.contentType || "Buy Now Content"}
                  </span>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>

      {/* Related */}
      <section className="ItemDetail__relatedSection">
        <div className="max-container">
          <div className="flex items-center justify-between">
            <h2 className="ItemDetail__relatedTitle">
              Related Sports Strategies Students Are Learning
            </h2>
            <Link to="/content" className="ItemDetail__learnMoreLink">
              Learn More Contents
            </Link>
          </div>
          <div className="ItemDetail__relatedGrid">
            {loadingRelated ? (
              <div className="loading-message">
                Loading related strategies...
              </div>
            ) : relatedStrategies.length > 0 ? (
              relatedStrategies.map((strategy) => (
                <StrategyCard
                  key={strategy._id}
                  id={strategy._id}
                  image={getImageUrl(strategy.thumbnailUrl) || "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"}
                  title={strategy.title}
                  coach={strategy.coachName}
                  price={
                    strategy.saleType === "Auction"
                      ? strategy.auctionDetails?.basePrice
                      : strategy.price
                  }
                  contentType={strategy.contentType}
                  type={strategy.saleType?.toLowerCase()}
                />
              ))
            ) : (
              <div className="no-content-message">
                No related strategies found for{" "}
                {content.sport || content.category}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Modals */}
      {content.saleType === "Auction" && (
        <>
          <BidModal
            isOpen={isBidModalOpen}
            onClose={() => setIsBidModalOpen(false)}
            strategy={content}
          />
          <OfferModal
            isOpen={isOfferModalOpen}
            onClose={() => setIsOfferModalOpen(false)}
            content={content}
          />
        </>
      )}
      {content.allowCustomRequests && (
        <RequestCustomTrainingModal
          isOpen={isRequestModalOpen}
          onClose={() => setIsRequestModalOpen(false)}
          strategy={content}
        />
      )}

      {/* Preview Modal */}
      <PreviewModal
        isOpen={isPreviewModalOpen}
        onClose={() => setIsPreviewModalOpen(false)}
        fileUrl={
          content.previewUrl?.startsWith("/uploads")
            ? IMAGE_BASE_URL + content.previewUrl
            : content.previewUrl || content.fileUrl
        }
        fileName={content.fileUrl?.split("/").pop() || content.title}
        title={content.title}
        contentType={content.contentType}
      />
    </div>
  );
};

export default ContentDetail;
